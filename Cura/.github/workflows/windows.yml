name: Windows Installer
run-name: ${{ inputs.cura_conan_version }} by @${{ github.actor }}

on:
  workflow_dispatch:
    inputs:
      cura_conan_version:
        description: 'Cura Conan Version'
        default: ''
        type: string
      package_overrides:
        description: 'List of specific packages to be used (space-separated)'
        default: ''
        type: string
      conan_args:
        description: 'Conan args'
        default: ''
        required: false
        type: string
      enterprise:
        description: 'Build Cura as an Enterprise edition'
        default: false
        required: true
        type: boolean
      staging:
        description: 'Use staging API'
        default: false
        required: true
        type: boolean
      operating_system:
        description: 'OS'
        required: true
        default: 'windows-2022'
        type: choice
        options:
          - self-hosted-Windows-X64
          - windows-2022

jobs:
  windows-installer:
    uses: wsd07/cura-workflows/.github/workflows/cura-installer-windows.yml@main
    with:
      cura_conan_version: ${{ inputs.cura_conan_version }}
      package_overrides: ${{ inputs.package_overrides }}
      conan_args: ${{ inputs.conan_args }}
      enterprise: ${{ inputs.enterprise }}
      staging: ${{ inputs.staging }}
      operating_system: ${{ inputs.operating_system }}
    secrets: inherit
